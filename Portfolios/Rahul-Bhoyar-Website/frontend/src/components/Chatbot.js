import React, { useState, useEffect, useRef } from 'react';
import './Chatbot.css';
import { FaPaperPlane, FaTimes } from 'react-icons/fa';
import { v4 as uuidv4 } from 'uuid';

const Chatbot = ({ onClose }) => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const messagesEndRef = useRef(null);
  const eventSourceRef = useRef(null);

  const scrollToBottom = () => {
    void messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  // Initialize session ID when component mounts
  useEffect(() => {
    if (!sessionId) {
      const newSessionId = uuidv4();
      setSessionId(newSessionId);
      console.log('Chat session started:', newSessionId);
    }
  }, [sessionId]);

  // Cleanup function for component unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (sessionId) {
        console.log('Chat session ended:', sessionId);
      }
    };
  }, [sessionId]);

  const handleSend = async () => {
    if (input.trim() && !isStreaming) {
      const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      const userMessage = { text: input, sender: 'user', timestamp };
      setMessages(prevMessages => [...prevMessages, userMessage]);
      const messageToSendToAPI = input;
      setInput('');
      setIsStreaming(true);
      setIsTyping(true);

      // Add typing indicator
      const typingMessage = {
        text: '',
        sender: 'bot',
        timestamp: '',
        isTyping: true,
        id: Date.now() // Unique ID for this message
      };
      setMessages(prevMessages => [...prevMessages, typingMessage]);

      try {
        // Use streaming endpoint with session ID
        const response = await fetch('/api/chat/stream', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            message: messageToSendToAPI,
            session_id: sessionId
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let streamedText = '';
        const botTimestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        // Set a timeout for the streaming response
        const timeoutId = setTimeout(() => {
          reader.cancel();
          setMessages(prevMessages => {
            const filtered = prevMessages.filter(msg => !msg.isTyping);
            return [...filtered, {
              text: "Sorry, the response took too long. Please try again.",
              sender: 'bot',
              timestamp: botTimestamp
            }];
          });
          setIsStreaming(false);
          setIsTyping(false);
        }, 30000); // 30 second timeout

        // Remove typing indicator and add actual message
        setMessages(prevMessages => {
          const filtered = prevMessages.filter(msg => !msg.isTyping);
          return [...filtered, {
            text: '',
            sender: 'bot',
            timestamp: botTimestamp,
            id: typingMessage.id,
            isStreaming: true
          }];
        });
        setIsTyping(false);

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));

                if (data.type === 'start') {
                  // Update session ID if provided
                  if (data.session_id && data.session_id !== sessionId) {
                    setSessionId(data.session_id);
                  }
                } else if (data.type === 'chunk') {
                  streamedText += data.content;
                  // Update the streaming message
                  setMessages(prevMessages =>
                    prevMessages.map(msg =>
                      msg.id === typingMessage.id
                        ? { ...msg, text: streamedText }
                        : msg
                    )
                  );
                } else if (data.type === 'end') {
                  // Mark streaming as complete and update session ID if provided
                  clearTimeout(timeoutId);
                  if (data.session_id && data.session_id !== sessionId) {
                    setSessionId(data.session_id);
                  }
                  setMessages(prevMessages =>
                    prevMessages.map(msg =>
                      msg.id === typingMessage.id
                        ? { ...msg, isStreaming: false }
                        : msg
                    )
                  );
                  break;
                } else if (data.type === 'error') {
                  clearTimeout(timeoutId);
                  streamedText = data.content;
                  setMessages(prevMessages =>
                    prevMessages.map(msg =>
                      msg.id === typingMessage.id
                        ? { ...msg, text: streamedText, isStreaming: false }
                        : msg
                    )
                  );
                  break;
                }
              } catch (parseError) {
                console.error('Error parsing SSE data:', parseError);
              }
            }
          }
        }
      } catch (error) {
        console.error("Error sending message:", error);
        const errorTimestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        setMessages(prevMessages => {
          const filtered = prevMessages.filter(msg => !msg.isTyping);
          return [...filtered, {
            text: "Sorry, I couldn't connect to the server.",
            sender: 'bot',
            timestamp: errorTimestamp
          }];
        });
        setIsTyping(false);
      } finally {
        setIsStreaming(false);
      }
    }
  };

  return (
    <div className={`chatbot-window ${isStreaming ? 'loading' : ''}`}>
      <div className="chatbot-header">
        <span>Chat With Me</span>
        <button onClick={onClose} className="chatbot-close-btn">
          <FaTimes />
        </button>
      </div>
      <div className="chatbot-messages">
        {messages.map((msg, index) => (
          <div key={msg.id || index} className={`message-container ${msg.sender}`}>
            <div className={`message ${msg.sender} ${msg.isStreaming ? 'streaming' : ''}`}>
              {msg.isTyping ? (
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              ) : (
                <>
                  {msg.text}
                  {msg.isStreaming && <span className="cursor">|</span>}
                </>
              )}
            </div>
            {msg.timestamp && <span className="message-timestamp">{msg.timestamp}</span>}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      <div className="chatbot-input">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSend()}
          placeholder={isStreaming ? "Please wait..." : "Type a message..."}
          disabled={isStreaming}
        />
        <button onClick={handleSend} disabled={isStreaming}>
          <FaPaperPlane />
        </button>
      </div>
    </div>
  );
};

export default Chatbot;