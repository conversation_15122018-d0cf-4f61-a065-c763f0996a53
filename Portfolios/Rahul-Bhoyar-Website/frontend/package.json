{"name": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-portfolio", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "bootstrap": "^5.3.2", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.20.0", "react-scripts": "^3.0.1", "uuid": "^8.3.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://backend:8000", "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.6"}}