from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import Dict, List, Optional, AsyncGenerator
from langchain_ollama import ChatOllama
from langchain_core.messages import AIMessage
from langchain_openai import Chat<PERSON>penAI
from langchain_groq import <PERSON>tG<PERSON>q
from dotenv import load_dotenv
import os
import json
import asyncio
from logging_config import setup_logger
from pydantic import BaseModel
from .system_prompt import SYSTEM_PROMPT_CONTENT
from .email_service import send_contact_email
from database import mongodb_crud
from database.mongodb_connection import connect_mongodb
# Load .env file
load_dotenv()
logger = setup_logger("routes.api")
router = APIRouter(prefix="/api", tags=["portfolio"])

# Sample data models/schemas
class Project(BaseModel):
    id: int
    title: str
    description: str
    technologies: List[str]
    category: str
    github_url: Optional[str] = None
    live_url: Optional[str] = None
    date: str
    image_url: Optional[str] = None

class Skill(BaseModel):
    name: str
    level: int  # 1-100
    category: str

class Experience(BaseModel):
    id: int
    company: str
    position: str
    start_date: str
    end_date: Optional[str] = None
    description: str
    technologies: List[str]
    achievements: Optional[List[str]] = None
    logo: Optional[str] = None
    company_url: Optional[str] = None
    location: Optional[str] = None

class Education(BaseModel):
    id: int
    institution: str
    degree: str
    field: str
    start_date: str
    end_date: Optional[str] = None
    description: Optional[str] = None
    achievements: Optional[List[str]] = None
    logo: Optional[str] = None
    institution_url: Optional[str] = None
    location: Optional[str] = None

class Certification(BaseModel):
    id: int
    title: str
    issuer: str
    date: str
    icon: str
    verification_url: Optional[str] = None

class Publication(BaseModel):
    id: int
    title: str
    authors: str
    journal: str
    year: str
    volume: Optional[str] = None
    issue: Optional[str] = None
    pages: Optional[str] = None
    doi: Optional[str] = None
    abstract: str
    keywords: List[str]
    pdf_link: Optional[str] = None
    external_link: Optional[str] = None
    category: str
    featured: bool = False
    published: bool = True

class BlogPost(BaseModel):
    id: int
    title: str
    excerpt: str
    date: str
    author: str
    category: str
    tags: List[str]
    image: Optional[str] = None
    featured: bool = False
    content: Optional[str] = None

class ContactMessage(BaseModel):
    name: str
    email: str
    subject: str
    message: str

class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None



#### Ollama Model #################################################
# MODEL_NAME = "mistral:7b"
# llm = ChatOllama(
#     base_url = "http://ollama:11434/",
#     model=MODEL_NAME,
#     temperature=4
# )
#####################################################



#### Docker Model Runner #################################################
# MODEL_NAME = "ai/llama3.2:3B-Q4_K_M"

# llm = ChatOpenAI(
#         model=MODEL_NAME,
#         base_url="http://host.docker.internal:12434/engines/v1",
#         api_key="ignored"
#     )

#####################################################


# #### OpenAI Model #################################################
# MODEL_NAME = "gpt-4.1"

# llm = ChatOpenAI(model=MODEL_NAME, temperature=0.7)

# #####################################################


#### Groq API Model #################################################
MODEL_NAME = "meta-llama/llama-4-scout-17b-16e-instruct"

llm = ChatGroq(model=MODEL_NAME, temperature=0.7)

#####################################################


def llm_response(user_message: str) -> AIMessage:
    messages = [("system",SYSTEM_PROMPT_CONTENT,),("human", user_message),]
    ai_msg = llm.invoke(messages)
    return ai_msg.content

async def llm_stream_response(user_message: str) -> AsyncGenerator[str, None]:
    """Stream response from LLM word by word"""
    messages = [("system", SYSTEM_PROMPT_CONTENT), ("human", user_message)]

    try:
        # Get the full response first
        ai_msg = llm.invoke(messages)
        response_text = ai_msg.content

        # Split response into words and stream them
        words = response_text.split()
        for i, word in enumerate(words):
            # Add space before word (except for first word)
            if i > 0:
                yield " "
            yield word
            # Add a small delay to simulate streaming
            await asyncio.sleep(0.05)  # 50ms delay between words

    except Exception as e:
        yield f"Error: {str(e)}"


# API Routes
@router.get("/")
def read_root():
    logger.info("Rahul Bhoyar Website - Root endpoint accessed")
    return {"message": "Welcome to Rahul Bhoyar's Portfolio API"}

@router.get("/health")
def health_check():
    """Health check endpoint to verify MongoDB connectivity."""
    try:
        # Test MongoDB connection by counting skills and projects
        skills_count = len(mongodb_crud.get_skills())
        projects_count = len(mongodb_crud.get_projects())

        return {
            "status": "healthy",
            "database": "MongoDB Atlas connected",
            "data_counts": {
                "skills": skills_count,
                "projects": projects_count
            },
            "message": "Portfolio API is running with MongoDB Atlas connectivity"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "MongoDB Atlas disconnected",
            "error": str(e),
            "message": "MongoDB Atlas connection failed"
        }

@router.get("/projects", response_model=List[Project])
def get_projects_endpoint():
    logger.info("Project Endpoint accessed")
    db_projects = mongodb_crud.get_projects()
    return [Project(
        id=int(p["id"][-6:], 16) if p["id"] else 0,  # Convert ObjectId to int for compatibility
        title=p["title"] or "",
        description=p["description"] or "",
        technologies=p["technologies"] or [],
        category=p["category"] or "",
        github_url=p["github_url"],
        live_url=p["live_url"],
        date=p["date"] or "",
        image_url=p["image_url"]
    ) for p in db_projects]

@router.get("/projects/{project_id}", response_model=Project)
def get_project_endpoint(project_id: str):
    db_project = mongodb_crud.get_project_by_id(project_id)
    if not db_project:
        raise HTTPException(status_code=404, detail="Project not found")
    return Project(
        id=int(db_project["id"][-6:], 16) if db_project["id"] else 0,
        title=db_project["title"] or "",
        description=db_project["description"] or "",
        technologies=db_project["technologies"] or [],
        category=db_project["category"] or "",
        github_url=db_project["github_url"],
        live_url=db_project["live_url"],
        date=db_project["date"] or "",
        image_url=db_project["image_url"]
    )

@router.get("/skills", response_model=List[Skill])
def get_skills_endpoint():
    logger.info("Skills Endpoint accessed")
    db_skills = mongodb_crud.get_skills()
    return [Skill(
        name=s["name"] or "",
        level=s["level"] or 50,
        category=s["category"] or ""
    ) for s in db_skills]

@router.get("/experience", response_model=List[Experience])
def get_experience_endpoint():
    logger.info("Experience Endpoint accessed")
    db_experiences = mongodb_crud.get_experiences()
    return [Experience(
        id=int(e["id"][-6:], 16) if e["id"] else 0,
        company=e["company"] or "",
        position=e["position"] or "",
        start_date=e["start_date"] or "",
        end_date=e["end_date"],
        description=e["description"] or "",
        technologies=e["technologies"] or [],
        achievements=e["achievements"] or [],
        logo=e["logo"],
        company_url=e["company_url"],
        location=e["location"]
    ) for e in db_experiences]

@router.get("/education", response_model=List[Education])
def get_education_endpoint():
    logger.info("Education Endpoint accessed")
    db_education = mongodb_crud.get_education()
    return [Education(
        id=int(e["id"][-6:], 16) if e["id"] else 0,
        degree=e["degree"] or "",
        field=e["field"] or "",
        institution=e["institution"] or "",
        start_date=e["start_date"] or "",
        end_date=e["end_date"],
        description=e["description"],
        achievements=e["achievements"] or [],
        logo=e["logo"],
        institution_url=e["institution_url"],
        location=e["location"]
    ) for e in db_education]

@router.get("/certifications", response_model=List[Certification])
def get_certifications_endpoint():
    logger.info("Certifications Endpoint accessed")
    db_certifications = mongodb_crud.get_certifications()
    return [Certification(
        id=int(c["id"][-6:], 16) if c["id"] else 0,
        title=c["title"] or "",
        issuer=c["issuer"] or "",
        date=c["date"] or "",
        icon=c["icon"] or "",
        verification_url=c["verification_url"]
    ) for c in db_certifications]

@router.get("/publications", response_model=List[Publication])
def get_publications_endpoint():
    logger.info("Publications Endpoint accessed")
    db_publications = mongodb_crud.get_publications()
    return [Publication(
        id=int(p["id"][-6:], 16) if p["id"] else 0,
        title=p["title"] or "",
        authors=p["authors"] or "",
        journal=p["journal"] or "",
        year=p["year"] or "",
        volume=p["volume"],
        issue=p["issue"],
        pages=p["pages"],
        doi=p["doi"],
        abstract=p["abstract"] or "",
        keywords=p["keywords"] or [],
        pdf_link=p["pdf_link"],
        external_link=p["external_link"],
        category=p["category"] or "",
        featured=p["featured"],
        published=p["published"]
    ) for p in db_publications]

@router.get("/blog", response_model=List[BlogPost])
def get_blog_posts_endpoint():
    db_blog_posts = mongodb_crud.get_blog_posts()
    return [BlogPost(
        id=b.get("id", 0),
        title=b.get("title", ""),
        excerpt=b.get("excerpt", ""),
        date=b.get("date", ""),
        author=b.get("author", ""),
        category=b.get("category", ""),
        tags=b.get("tags", []),
        image=b.get("image"),
        featured=b.get("featured", False),
        content=b.get("content", "")
    ) for b in db_blog_posts]

@router.get("/blog/{post_id}", response_model=BlogPost)
def get_blog_post_endpoint(post_id: int):
    db_blog_posts = mongodb_crud.get_blog_posts()
    for post in db_blog_posts:
        if post.get("id") == post_id:
            return BlogPost(
                id=post.get("id", 0),
                title=post.get("title", ""),
                excerpt=post.get("excerpt", ""),
                date=post.get("date", ""),
                author=post.get("author", ""),
                category=post.get("category", ""),
                tags=post.get("tags", []),
                image=post.get("image"),
                featured=post.get("featured", False),
                content=post.get("content", "")
            )
    raise HTTPException(status_code=404, detail="Blog post not found")

@router.post("/contact", status_code=201)
def send_message(message: ContactMessage):
    """Handle contact form submission and send email"""
    logger.info("Contact Endpoint accessed")
    try:
        # Store message in database with duplicate prevention
        db_message = mongodb_crud.create_contact_message(
            name=message.name,
            email=message.email,
            subject=message.subject,
            message=message.message
        )

        # Check if it's a duplicate
        if db_message.get("error") == "duplicate":
            logger.warning(f"Duplicate submission detected from {message.email}")
            raise HTTPException(
                status_code=409,
                detail="This message has already been submitted. Please wait for a response or submit a different message."
            )

        # Check for database errors
        if db_message.get("error") == "database_error":
            logger.error(f"Database error: {db_message.get('message')}")
            raise HTTPException(
                status_code=500,
                detail="Failed to store your message. Please try again later."
            )

        # Send email using the email service
        send_contact_email(
            name=message.name,
            email=message.email,
            subject=message.subject,
            message=message.message
        )

        # Mark message as processed (placeholder)
        mongodb_crud.mark_message_processed(db_message.get("id", "temp"))

        print(f"✅ Contact form processed successfully from {message.name}: {message.subject}")
        return {
            "status": "success",
            "message": "Your message has been sent successfully! I'll get back to you soon.",
            "message_id": db_message.get("id", "temp")
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise

    except ValueError as ve:
        # Configuration error
        print(f"❌ Configuration error: {ve}")
        raise HTTPException(
            status_code=500,
            detail="Email service configuration error. Please contact the administrator."
        )

    except Exception as e:
        # General error
        print(f"❌ Failed to send contact email: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to send your message. Please try again later or contact me directly."
        )

@router.post("/chat", response_model=Dict[str, str])
async def chat_endpoint(chat_message: ChatMessage):
    logger.info("Chatbot Endpoint accessed")
    reply_text = llm_response(chat_message.message)

    # Store chat log in database with session management
    chat_log = mongodb_crud.create_chat_log(
        user_message=chat_message.message,
        bot_response=reply_text,
        session_id=chat_message.session_id
    )

    return {
        "reply": reply_text,
        "session_id": chat_log.get("session_id", "")
    }

# Admin endpoints for managing contact messages and chat logs
@router.get("/admin/contact-messages")
def get_contact_messages_endpoint(processed: Optional[bool] = None):
    """Get all contact messages (admin endpoint)"""
    messages = mongodb_crud.get_contact_messages(processed=processed)
    return [
        {
            "id": msg.get("id", ""),
            "name": msg.get("name", ""),
            "email": msg.get("email", ""),
            "subject": msg.get("subject", ""),
            "message": msg.get("message", ""),
            "created_at": msg.get("created_at", ""),
            "processed": msg.get("processed", False)
        }
        for msg in messages
    ]

@router.get("/admin/chat-logs")
def get_chat_logs_endpoint(session_id: Optional[str] = None):
    """Get all chat logs (admin endpoint)"""
    logs = mongodb_crud.get_chat_logs(session_id=session_id)
    return [
        {
            "id": log.get("id", ""),
            "user_message": log.get("user_message", ""),
            "bot_response": log.get("bot_response", ""),
            "created_at": log.get("created_at", ""),
            "session_id": log.get("session_id", "")
        }
        for log in logs
    ]

@router.put("/admin/contact-messages/{message_id}/processed")
def mark_message_processed_endpoint(message_id: str):
    """Mark a contact message as processed (admin endpoint)"""
    message = mongodb_crud.mark_message_processed(message_id)
    if not message:
        raise HTTPException(status_code=404, detail="Message not found")
    return {"success": True, "message": "Message marked as processed"}

@router.post("/chat/stream")
async def chat_stream_endpoint(chat_message: ChatMessage):
    """Stream chat response using Server-Sent Events"""
    logger.info("Chatbot Endpoint accessed")

    # Store the full response for database logging
    full_response = ""

    async def generate_stream():
        nonlocal full_response
        try:
            # Send initial event to indicate streaming started
            yield f"data: {json.dumps({'type': 'start', 'content': '', 'session_id': chat_message.session_id or ''})}\n\n"

            # Stream the response word by word
            async for word_chunk in llm_stream_response(chat_message.message):
                full_response += word_chunk
                data = {
                    'type': 'chunk',
                    'content': word_chunk
                }
                yield f"data: {json.dumps(data)}\n\n"

            # Store chat log in database with session management
            chat_log = mongodb_crud.create_chat_log(
                user_message=chat_message.message,
                bot_response=full_response,
                session_id=chat_message.session_id
            )

            # Send end event with session ID
            yield f"data: {json.dumps({'type': 'end', 'content': '', 'session_id': chat_log.get('session_id', '')})}\n\n"

        except Exception as e:
            # Send error event
            error_data = {
                'type': 'error',
                'content': f"Sorry, I couldn't process your message: {str(e)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )
