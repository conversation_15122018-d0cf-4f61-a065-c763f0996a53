"""
MongoDB CRUD operations for the portfolio database using MongoEngine.
These functions replace the SQLite CRUD operations and maintain compatibility with existing API endpoints.
"""

from typing import List, Optional, Dict, Any
from .mongodb_models import Experiences, Skills, Projects, Education, Certifications, Publications, EmailSubmissions, ChatLogs
from logging_config import setup_logger
import json
from datetime import datetime
import uuid

logger = setup_logger("database.mongodb_crud")


# --- SKILLS ---

def get_skills() -> List[Dict[str, Any]]:
    """Get all skills from MongoDB."""
    try:
        skills = Skills.objects()
        return [
            {
                "name": skill.name,
                "level": int(skill.level) if skill.level and skill.level.isdigit() else 50,  # Convert to int for compatibility
                "category": skill.category
            }
            for skill in skills
        ]
    except Exception as e:
        logger.error(f"Error fetching skills: {e}")
        return []


# --- EXPERIENCES ---

def get_experiences() -> List[Dict[str, Any]]:
    """Get all experiences from MongoDB."""
    try:
        experiences = Experiences.objects()
        result = []
        for exp in experiences:
            # Parse technologies string to list
            technologies = []
            if exp.technologies:
                try:
                    # Try to parse as JSON first
                    technologies = json.loads(exp.technologies)
                except:
                    # If not JSON, split by comma
                    technologies = [tech.strip() for tech in exp.technologies.split(',') if tech.strip()]
            
            # Parse achievements string to list
            achievements = []
            if exp.achievements:
                try:
                    achievements = json.loads(exp.achievements)
                except:
                    achievements = [ach.strip() for ach in exp.achievements.split(',') if ach.strip()]
            
            result.append({
                "id": str(exp.id),  # Convert ObjectId to string
                "company": exp.company,
                "position": exp.position,
                "start_date": exp.start_date,
                "end_date": exp.end_date,
                "description": exp.description,
                "technologies": technologies,
                "achievements": achievements,
                "logo": exp.logo,
                "company_url": exp.company_url,
                "location": exp.location
            })
        return result
    except Exception as e:
        logger.error(f"Error fetching experiences: {e}")
        return []


# --- EDUCATION ---

def get_education() -> List[Dict[str, Any]]:
    """Get all education records from MongoDB."""
    try:
        education_records = Education.objects()
        result = []
        for edu in education_records:
            # Parse achievements string to list
            achievements = []
            if edu.achievements:
                try:
                    achievements = json.loads(edu.achievements)
                except:
                    achievements = [ach.strip() for ach in edu.achievements.split(',') if ach.strip()]
            
            result.append({
                "id": str(edu.id),
                "degree": edu.degree,
                "field": edu.field,
                "institution": edu.institution,
                "start_date": str(edu.start_date) if edu.start_date else None,
                "end_date": str(edu.end_date) if edu.end_date else None,
                "description": edu.description,
                "achievements": achievements,
                "logo": edu.logo,
                "institution_url": edu.institution_url,
                "location": edu.location
            })
        return result
    except Exception as e:
        logger.error(f"Error fetching education: {e}")
        return []


# --- CERTIFICATIONS ---

def get_certifications() -> List[Dict[str, Any]]:
    """Get all certifications from MongoDB."""
    try:
        certifications = Certifications.objects()
        return [
            {
                "id": str(cert.id),
                "title": cert.title,
                "issuer": cert.issuer,
                "date": cert.date,
                "icon": cert.icon,
                "verification_url": cert.verification_url
            }
            for cert in certifications
        ]
    except Exception as e:
        logger.error(f"Error fetching certifications: {e}")
        return []


# --- PROJECTS ---

def get_projects() -> List[Dict[str, Any]]:
    """Get all projects from MongoDB."""
    try:
        projects = Projects.objects()
        result = []
        for project in projects:
            # Parse technologies string to list
            technologies = []
            if project.technologies:
                try:
                    technologies = json.loads(project.technologies)
                except:
                    technologies = [tech.strip() for tech in project.technologies.split(',') if tech.strip()]
            
            result.append({
                "id": str(project.id),
                "title": project.title,
                "description": project.description,
                "technologies": technologies,
                "category": project.category,
                "github_url": project.github_url,
                "live_url": project.live_url,
                "date": project.date,
                "image_url": project.image_url
            })
        return result
    except Exception as e:
        logger.error(f"Error fetching projects: {e}")
        return []


def get_project_by_id(project_id: str) -> Optional[Dict[str, Any]]:
    """Get a specific project by ID from MongoDB."""
    try:
        project = Projects.objects(id=project_id).first()
        if not project:
            return None
        
        # Parse technologies string to list
        technologies = []
        if project.technologies:
            try:
                technologies = json.loads(project.technologies)
            except:
                technologies = [tech.strip() for tech in project.technologies.split(',') if tech.strip()]
        
        return {
            "id": str(project.id),
            "title": project.title,
            "description": project.description,
            "technologies": technologies,
            "category": project.category,
            "github_url": project.github_url,
            "live_url": project.live_url,
            "date": project.date,
            "image_url": project.image_url
        }
    except Exception as e:
        logger.error(f"Error fetching project {project_id}: {e}")
        return None


# --- PUBLICATIONS ---

def get_publications() -> List[Dict[str, Any]]:
    """Get all publications from MongoDB."""
    try:
        publications = Publications.objects()
        result = []
        for pub in publications:
            # Parse keywords string to list
            keywords = []
            if pub.keywords:
                try:
                    keywords = json.loads(pub.keywords)
                except:
                    keywords = [kw.strip() for kw in pub.keywords.split(',') if kw.strip()]

            result.append({
                "id": str(pub.id),
                "title": pub.title,
                "authors": pub.authors,
                "journal": pub.journal,
                "year": str(pub.year) if pub.year else None,
                "volume": pub.volume,
                "issue": pub.issue,
                "pages": pub.pages,
                "doi": pub.doi,
                "abstract": pub.abstract,
                "keywords": keywords,
                "pdf_link": pub.pdf_link,
                "external_link": pub.external_link,
                "category": pub.category,
                "featured": pub.published if pub.published is not None else True,  # Use published field for featured
                "published": pub.published if pub.published is not None else True
            })
        return result
    except Exception as e:
        logger.error(f"Error fetching publications: {e}")
        return []


# --- PLACEHOLDER FUNCTIONS FOR COMPATIBILITY ---
# These functions are needed for compatibility with existing API endpoints
# but are not part of the main portfolio data migration

def get_blog_posts() -> List[Dict[str, Any]]:
    """Placeholder for blog posts - returns empty list for now."""
    logger.info("Blog posts not implemented in MongoDB yet")
    return []


def create_contact_message(name: str, email: str, subject: str, message: str) -> Dict[str, Any]:
    """Create a new email submission in MongoDB with duplicate prevention."""
    try:
        # Check for duplicates
        if EmailSubmissions.check_duplicate(name, email, subject, message):
            logger.warning(f"Duplicate email submission detected from {email}")
            return {"error": "duplicate", "message": "This message has already been submitted"}

        # Create timestamp strings
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        timestamp_str = now.strftime("%Y-%m-%d %H:%M:%S")

        # Create new submission
        submission = EmailSubmissions(
            name=name,
            email=email,
            subject=subject,
            message=message,
            date=date_str,
            time_stamp=timestamp_str
        )
        submission.save()

        logger.info(f"Email submission created with ID: {submission.id}")
        return {
            "id": str(submission.id),
            "name": name,
            "email": email,
            "subject": subject,
            "message": message,
            "date": date_str,
            "time_stamp": timestamp_str
        }
    except Exception as e:
        logger.error(f"Error creating email submission: {e}")
        return {"error": "database_error", "message": str(e)}


def get_contact_messages(processed: Optional[bool] = None) -> List[Dict[str, Any]]:
    """Get all email submissions from MongoDB, optionally filtered by processed status."""
    try:
        # For now, we don't have a processed field, so we'll return all messages
        # This can be extended later if needed
        submissions = EmailSubmissions.objects()

        result = []
        for submission in submissions:
            result.append({
                "id": str(submission.id),
                "name": submission.name,
                "email": submission.email,
                "subject": submission.subject,
                "message": submission.message,
                "date": submission.date,
                "time_stamp": submission.time_stamp,
                "created_at": submission.created_at.isoformat() if submission.created_at else None,
                "processed": False  # Default to False since we don't track this yet
            })

        logger.info(f"Retrieved {len(result)} email submissions")
        return result
    except Exception as e:
        logger.error(f"Error fetching email submissions: {e}")
        return []


def mark_message_processed(message_id: str) -> Optional[Dict[str, Any]]:
    """Mark an email submission as processed (placeholder implementation)."""
    try:
        # For now, we'll just return the message if it exists
        # This can be extended later to add a processed field to the model
        submission = EmailSubmissions.objects(id=int(message_id)).first()
        if submission:
            logger.info(f"Message {message_id} marked as processed (placeholder)")
            return {
                "id": str(submission.id),
                "name": submission.name,
                "email": submission.email,
                "subject": submission.subject,
                "message": submission.message,
                "processed": True
            }
        return None
    except Exception as e:
        logger.error(f"Error marking message as processed: {e}")
        return None


def create_chat_log(user_message: str, bot_response: str, session_id: Optional[str] = None) -> Dict[str, Any]:
    """Create a new chat log entry in MongoDB."""
    try:
        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())

        # Create timestamp strings
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        timestamp_str = now.strftime("%Y-%m-%d %H:%M:%S")

        # Create new chat log
        chat_log = ChatLogs(
            session_id=session_id,
            user_message=user_message,
            bot_message=bot_response,
            date=date_str,
            time_stamp=timestamp_str
        )
        chat_log.save()

        logger.info(f"Chat log created with ID: {chat_log.id} for session: {session_id}")
        return {
            "id": str(chat_log.id),
            "session_id": session_id,
            "user_message": user_message,
            "bot_response": bot_response,
            "date": date_str,
            "time_stamp": timestamp_str
        }
    except Exception as e:
        logger.error(f"Error creating chat log: {e}")
        return {"error": "database_error", "message": str(e)}


def get_chat_logs(session_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get chat logs from MongoDB, optionally filtered by session ID."""
    try:
        # Filter by session_id if provided
        if session_id:
            chat_logs = ChatLogs.objects(session_id=session_id)
        else:
            chat_logs = ChatLogs.objects()

        result = []
        for log in chat_logs:
            result.append({
                "id": str(log.id),
                "session_id": log.session_id,
                "user_message": log.user_message,
                "bot_response": log.bot_message,
                "date": log.date,
                "time_stamp": log.time_stamp,
                "created_at": log.created_at.isoformat() if log.created_at else None
            })

        logger.info(f"Retrieved {len(result)} chat logs" + (f" for session {session_id}" if session_id else ""))
        return result
    except Exception as e:
        logger.error(f"Error fetching chat logs: {e}")
        return []
