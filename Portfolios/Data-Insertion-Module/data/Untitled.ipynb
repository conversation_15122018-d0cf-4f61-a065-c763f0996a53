{"cells": [{"cell_type": "code", "execution_count": 3, "id": "33ddee78-8f03-4791-957d-8f9cd440ee46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pandas\n", "  Downloading pandas-2.3.1-cp39-cp39-macosx_11_0_arm64.whl (10.8 MB)\n", "\u001b[K     |████████████████████████████████| 10.8 MB 2.5 MB/s eta 0:00:01\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/lib/python3.9/site-packages (from pandas) (2.9.0.post0)\n", "Collecting numpy>=1.22.4\n", "  Downloading numpy-2.0.2-cp39-cp39-macosx_14_0_arm64.whl (5.3 MB)\n", "\u001b[K     |████████████████████████████████| 5.3 MB 7.1 MB/s eta 0:00:01\n", "\u001b[?25hCollecting tzdata>=2022.7\n", "  Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)\n", "\u001b[K     |████████████████████████████████| 347 kB 18.5 MB/s eta 0:00:01\n", "\u001b[?25hCollecting pytz>=2020.1\n", "  Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)\n", "\u001b[K     |████████████████████████████████| 509 kB 10.0 MB/s eta 0:00:01\n", "\u001b[?25hRequirement already satisfied: six>=1.5 in /Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/lib/python3.9/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Installing collected packages: tzdata, pytz, numpy, pandas\n", "Successfully installed numpy-2.0.2 pandas-2.3.1 pytz-2025.2 tzdata-2025.2\n", "\u001b[33mWARNING: You are using pip version 21.2.4; however, version 25.1.1 is available.\n", "You should consider upgrading via the '/Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/bin/python3 -m pip install --upgrade pip' command.\u001b[0m\n"]}], "source": ["!pip install pandas"]}, {"cell_type": "markdown", "id": "84708ca6-6e21-47a2-b9d5-3c32d7cc0dd2", "metadata": {}, "source": ["## (A) Inserting Projects"]}, {"cell_type": "code", "execution_count": 94, "id": "64e87564-06b0-44ee-a66a-439b66082110", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 1 new unique project(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load credentials from .env\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"projects.csv\"  # 🔁 Update this with your CSV file path\n", "projects = pd.read_csv(csv_path)\n", "\n", "# Replace NaN with empty strings\n", "projects = projects.fillna('')\n", "\n", "# Reorder columns and set 'id' as index\n", "projects = projects[[\"id\", \"title\", \"description\", \"category\", \"date\", \"image_url\", \"technologies\", \"github_url\", \"live_url\"]]\n", "projects = projects.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Project(Document):\n", "    id = IntField(primary_key=True)\n", "    title = StringField()\n", "    description = StringField()\n", "    category = StringField()\n", "    date = StringField()\n", "    image_url = StringField()\n", "    technologies = StringField()\n", "    github_url = StringField()\n", "    live_url = StringField()\n", "\n", "    meta = {'collection': 'projects'}\n", "\n", "# Insert unique records\n", "inserted_count = 0\n", "for idx, row in projects.iterrows():\n", "    try:\n", "        # Skip if record with this ID already exists\n", "        Project.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        # Insert new record\n", "        doc = Project(\n", "            id=idx,\n", "            title=str(row['title']),\n", "            description=str(row['description']),\n", "            category=str(row['category']),\n", "            date=str(row['date']),\n", "            image_url=str(row['image_url']),\n", "            technologies=str(row['technologies']),\n", "            github_url=str(row['github_url']),\n", "            live_url=str(row['live_url']),\n", "        )\n", "        doc.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique project(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "74db5f54-3e16-45ea-9f59-a150e1c32f3c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "43c62218-3a64-400c-9fea-a469c9d1d9ee", "metadata": {}, "source": ["## (B) Insert Certifications"]}, {"cell_type": "code", "execution_count": 25, "id": "264188ec-0aa2-4196-a52a-9b6253ae9aac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique certification(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"certifications.csv\"  # 🔁 Update with your path\n", "certifications = pd.read_csv(csv_path)\n", "\n", "# Replace NaN with empty strings\n", "certifications = certifications.fillna('')\n", "\n", "# Reorder columns (optional, based on your preferred order)\n", "certifications = certifications[[\"id\", \"title\", \"issuer\", \"date\", \"verification_url\", \"icon\"]]\n", "certifications = certifications.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Certification(Document):\n", "    id = IntField(primary_key=True)\n", "    title = StringField()\n", "    issuer = StringField()\n", "    date = StringField()\n", "    verification_url = StringField()\n", "    icon = StringField()\n", "\n", "    meta = {'collection': 'certifications'}\n", "\n", "# Insert unique records only\n", "inserted_count = 0\n", "for idx, row in certifications.iterrows():\n", "    try:\n", "        # Check if certification with same id exists\n", "        Certification.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        # Insert if not found\n", "        cert = Certification(\n", "            id=idx,\n", "            title=str(row['title']),\n", "            issuer=str(row['issuer']),\n", "            date=str(row['date']),\n", "            verification_url=str(row['verification_url']),\n", "            icon=str(row['icon']),\n", "        )\n", "        cert.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique certification(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "50abcf6c-ff1c-43c7-830d-4f734d72ec1a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d395c597-b495-4140-b769-f92d50d84e12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e323ec53-6944-4c1c-b4fe-d318a59daff4", "metadata": {}, "source": ["## (C) Education"]}, {"cell_type": "code", "execution_count": 38, "id": "0043291a-53f9-47da-b150-21ad9b2c7433", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique education record(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"education.csv\"  # 🔁 Update to your file path\n", "education = pd.read_csv(csv_path)\n", "\n", "# Replace NaN with empty strings\n", "education = education.fillna('')\n", "\n", "# Reorder columns\n", "education = education[[\n", "    \"id\", \"degree\", \"field\", \"institution\", \"description\",\n", "    \"location\", \"start_date\", \"end_date\", \"achievements\",\n", "    \"institution_url\", \"logo\"\n", "]]\n", "\n", "# Set 'id' as index\n", "education = education.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Education(Document):\n", "    id = IntField(primary_key=True)\n", "    degree = StringField()\n", "    field = StringField()\n", "    institution = StringField()\n", "    description = StringField()\n", "    location = StringField()\n", "    start_date = IntField()\n", "    end_date = IntField()\n", "    achievements = StringField()\n", "    institution_url = StringField()\n", "    logo = StringField()\n", "\n", "    meta = {'collection': 'education'}\n", "\n", "# Insert unique records\n", "inserted_count = 0\n", "for idx, row in education.iterrows():\n", "    try:\n", "        Education.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        edu = Education(\n", "            id=idx,\n", "            degree=str(row['degree']),\n", "            field=str(row['field']),\n", "            institution=str(row['institution']),\n", "            description=str(row['description']),\n", "            location=str(row['location']),\n", "            start_date=int(row['start_date']),\n", "            end_date=int(row['end_date']),\n", "            achievements=str(row['achievements']),\n", "            institution_url=str(row['institution_url']),\n", "            logo=str(row['logo']),\n", "        )\n", "        edu.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique education record(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "1c2e5344-00f7-41e1-aa2e-74e47c4fc8ea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "99711619-49bb-47af-a954-25e19d365144", "metadata": {}, "source": ["## (D) Experiences"]}, {"cell_type": "code", "execution_count": 47, "id": "081e2398-f832-451b-8048-ecd1555bf795", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique experience record(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"experiences.csv\"  # 🔁 Update this path to your CSV\n", "experiences = pd.read_csv(csv_path)\n", "\n", "# Replace NaN with empty strings\n", "experiences = experiences.fillna('')\n", "\n", "# Reorder columns (for consistency)\n", "experiences = experiences[[\n", "    \"id\", \"company\", \"position\", \"description\", \"location\",\n", "    \"achievements\", \"company_url\", \"start_date\", \"end_date\",\n", "    \"technologies\", \"logo\"\n", "]]\n", "\n", "# Set 'id' as index\n", "experiences = experiences.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Experience(Document):\n", "    id = IntField(primary_key=True)\n", "    company = StringField()\n", "    position = StringField()\n", "    description = StringField()\n", "    location = StringField()\n", "    achievements = StringField()\n", "    company_url = StringField()\n", "    start_date = StringField()\n", "    end_date = StringField()\n", "    technologies = StringField()\n", "    logo = StringField()\n", "\n", "    meta = {'collection': 'experiences'}\n", "\n", "# Insert only unique records\n", "inserted_count = 0\n", "for idx, row in experiences.iterrows():\n", "    try:\n", "        Experience.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        exp = Experience(\n", "            id=idx,\n", "            company=str(row['company']),\n", "            position=str(row['position']),\n", "            description=str(row['description']),\n", "            location=str(row['location']),\n", "            achievements=str(row['achievements']),\n", "            company_url=str(row['company_url']),\n", "            start_date=str(row['start_date']),\n", "            end_date=str(row['end_date']),\n", "            technologies=str(row['technologies']),\n", "            logo=str(row['logo']),\n", "        )\n", "        exp.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique experience record(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "ae440593-1ad3-429d-808d-6474e57fc833", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "4f722e21-8695-4fdd-848f-2a57ec843bc8", "metadata": {}, "source": ["## (E) Publications"]}, {"cell_type": "code", "execution_count": 78, "id": "656485db-cbc8-4d0d-a2b1-5a794bbb522c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique publication(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, BooleanField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load .env environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV\n", "csv_path = \"publications.csv\"  # 🔁 Update this path\n", "publications = pd.read_csv(csv_path)\n", "\n", "# Drop rows with missing 'id' and cast to int\n", "publications = publications[publications['id'].notna()]\n", "publications['id'] = publications['id'].astype(int)\n", "\n", "# Replace remaining NaNs with empty string\n", "publications = publications.fillna('')\n", "\n", "# Reorder columns (optional)\n", "publications = publications[[\n", "    \"id\", \"title\", \"authors\", \"abstract\", \"issue\", \"category\", \n", "    \"pages\", \"year\", \"keywords\", \"published\", \"external_link\", \n", "    \"pdf_link\", \"journal\", \"volume\", \"doi\"\n", "]]\n", "\n", "# Set 'id' as index\n", "publications = publications.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Publication(Document):\n", "    id = IntField(primary_key=True)\n", "    title = StringField()\n", "    authors = StringField()\n", "    abstract = StringField()\n", "    issue = StringField()\n", "    category = StringField()\n", "    pages = StringField()\n", "    year = IntField()\n", "    keywords = StringField()\n", "    published = BooleanField()\n", "    external_link = StringField()\n", "    pdf_link = StringField()\n", "    journal = StringField()\n", "    volume = StringField()\n", "    doi = StringField()\n", "\n", "    meta = {'collection': 'publications'}\n", "\n", "# Insert unique records\n", "inserted_count = 0\n", "for idx, row in publications.iterrows():\n", "    try:\n", "        Publication.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        pub = Publication(\n", "            id=idx,\n", "            title=str(row['title']),\n", "            authors=str(row['authors']),\n", "            abstract=str(row['abstract']),\n", "            issue=str(row['issue']),\n", "            category=str(row['category']),\n", "            pages=str(row['pages']),\n", "            year=int(row['year']),\n", "            keywords=str(row['keywords']),\n", "            published=bool(row['published']),\n", "            external_link=str(row['external_link']),\n", "            pdf_link=str(row['pdf_link']),\n", "            journal=str(row['journal']),\n", "            volume=str(row['volume']),\n", "            doi=str(row['doi']),\n", "        )\n", "        pub.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique publication(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "8554a13d-36df-4eba-b709-7b5297f81109", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "5ed788dc-cdbf-4903-a319-4b7a76cf4530", "metadata": {}, "source": ["## (F) Skills"]}, {"cell_type": "code", "execution_count": 85, "id": "309c93b0-1cf8-4d36-9f72-122198038abc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique skill(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "\n", "# Load environment variables from .env\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Load CSV (or assume skills DataFrame already exists)\n", "csv_path = \"skills.csv\"  # 🔁 Update path if needed\n", "skills = pd.read_csv(csv_path)\n", "\n", "# Add id column starting from 1 (if not already present)\n", "if 'id' not in skills.columns:\n", "    skills['id'] = range(1, len(skills) + 1)\n", "\n", "# Fill NaNs and reorder columns\n", "skills = skills.fillna('')\n", "skills = skills[['id', 'name', 'level', 'category']]\n", "skills = skills.set_index(\"id\")\n", "\n", "# Define MongoDB Document\n", "class Skill(Document):\n", "    id = IntField(primary_key=True)\n", "    name = StringField()\n", "    level = StringField()\n", "    category = StringField()\n", "\n", "    meta = {'collection': 'skills'}\n", "\n", "# Insert only unique records\n", "inserted_count = 0\n", "for idx, row in skills.iterrows():\n", "    try:\n", "        Skill.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        skill = Skill(\n", "            id=idx,\n", "            name=str(row['name']),\n", "            level=str(row['level']),\n", "            category=str(row['category']),\n", "        )\n", "        skill.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique skill(s) inserted successfully!\")\n"]}, {"cell_type": "markdown", "id": "a6f72e41-9e11-45e9-9f3c-455d186069d8", "metadata": {}, "source": ["## (G) Email Submissions"]}, {"cell_type": "code", "execution_count": 9, "id": "b915e814-e5dd-4bfc-8bcb-8aecd652139c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>email</th>\n", "      <th>subject</th>\n", "      <th>message</th>\n", "      <th>date</th>\n", "      <th>time_stamp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>Support</td>\n", "      <td>I want to change my subscription.</td>\n", "      <td>2025-07-17</td>\n", "      <td>2025-07-17T17:18:17.796889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>Request</td>\n", "      <td>Can you provide more details?</td>\n", "      <td>2025-07-14</td>\n", "      <td>2025-07-14T04:14:17.797083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td><EMAIL></td>\n", "      <td>Feedback</td>\n", "      <td>Thank you for your prompt response.</td>\n", "      <td>2025-07-10</td>\n", "      <td>2025-07-10T14:41:17.797093</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id    name              email   subject  \\\n", "0   1  Sophia  <EMAIL>   Support   \n", "1   2    <PERSON>  <EMAIL>   Request   \n", "2   3  Sophia  <EMAIL>  Feedback   \n", "\n", "                               message        date                  time_stamp  \n", "0    I want to change my subscription.  2025-07-17  2025-07-17T17:18:17.796889  \n", "1        Can you provide more details?  2025-07-14  2025-07-14T04:14:17.797083  \n", "2  Thank you for your prompt response.  2025-07-10  2025-07-10T14:41:17.797093  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load CSV\n", "import pandas as pd\n", "csv_path = \"email_submissions.csv\"  # 🔁 Update this path\n", "email_submissions = pd.read_csv(csv_path)\n", "email_submissions"]}, {"cell_type": "code", "execution_count": 11, "id": "fdcba928-9b4c-4291-94ec-164c88fa01ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3 entries, 0 to 2\n", "Data columns (total 7 columns):\n", " #   Column      Non-Null Count  Dtype \n", "---  ------      --------------  ----- \n", " 0   id          3 non-null      int64 \n", " 1   name        3 non-null      object\n", " 2   email       3 non-null      object\n", " 3   subject     3 non-null      object\n", " 4   message     3 non-null      object\n", " 5   date        3 non-null      object\n", " 6   time_stamp  3 non-null      object\n", "dtypes: int64(1), object(6)\n", "memory usage: 296.0+ bytes\n"]}], "source": ["email_submissions.info()"]}, {"cell_type": "code", "execution_count": 13, "id": "db24661e-835e-4bf9-b600-bd1c6ffd3eb9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 3 new unique email submission(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DateTimeField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "from datetime import datetime\n", "\n", "# Load .env environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Simulate or load the DataFrame\n", "# If email_submissions is already defined earlier, skip loading from CSV\n", "# Otherwise, load like:\n", "# email_submissions = pd.read_csv(\"email_submissions.csv\")\n", "\n", "# Convert time_stamp to datetime object\n", "email_submissions[\"time_stamp\"] = pd.to_datetime(email_submissions[\"time_stamp\"])\n", "\n", "# Define MongoDB Document\n", "class EmailSubmission(Document):\n", "    id = IntField(primary_key=True)\n", "    name = StringField()\n", "    email = StringField()\n", "    subject = StringField()\n", "    message = StringField()\n", "    date = StringField()  # You can keep it as StringField (YYYY-MM-DD) or convert to DateField\n", "    time_stamp = DateTimeField()\n", "\n", "    meta = {'collection': 'email_submissions'}\n", "\n", "# Insert unique records\n", "inserted_count = 0\n", "for idx, row in email_submissions.iterrows():\n", "    try:\n", "        EmailSubmission.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        submission = EmailSubmission(\n", "            id=idx,\n", "            name=row['name'],\n", "            email=row['email'],\n", "            subject=row['subject'],\n", "            message=row['message'],\n", "            date=row['date'],\n", "            time_stamp=row['time_stamp']\n", "        )\n", "        submission.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique email submission(s) inserted successfully!\")\n"]}, {"cell_type": "markdown", "id": "3e341ed6-5e1d-4e74-bf19-f80743648369", "metadata": {}, "source": ["## (H) <PERSON><PERSON> Logs"]}, {"cell_type": "code", "execution_count": 16, "id": "1313505f-d48a-4aba-b8fa-f76bdfde0332", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>session_id</th>\n", "      <th>user_message</th>\n", "      <th>bot_message</th>\n", "      <th>date</th>\n", "      <th>time_stamp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>923b4d74-5973-469f-9225-4d704a78aaba</td>\n", "      <td>Sample user message 1</td>\n", "      <td>Sample bot response 1</td>\n", "      <td>2025-07-14</td>\n", "      <td>2025-07-14 20:16:51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>b7dada83-de55-4554-b431-534881626331</td>\n", "      <td>Sample user message 2</td>\n", "      <td>Sample bot response 2</td>\n", "      <td>2025-06-30</td>\n", "      <td>2025-06-30 08:06:51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>676bed85-b670-4a46-8ab4-151b75f1b6f5</td>\n", "      <td>Sample user message 3</td>\n", "      <td>Sample bot response 3</td>\n", "      <td>2025-07-08</td>\n", "      <td>2025-07-08 22:39:51</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id                            session_id           user_message  \\\n", "0   1  923b4d74-5973-469f-9225-4d704a78aaba  Sample user message 1   \n", "1   2  b7dada83-de55-4554-b431-534881626331  <PERSON><PERSON> user message 2   \n", "2   3  676bed85-b670-4a46-8ab4-151b75f1b6f5  Sample user message 3   \n", "\n", "             bot_message        date           time_stamp  \n", "0  Sample bot response 1  2025-07-14  2025-07-14 20:16:51  \n", "1  Sample bot response 2  2025-06-30  2025-06-30 08:06:51  \n", "2  Sample bot response 3  2025-07-08  2025-07-08 22:39:51  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load CSV\n", "import pandas as pd\n", "csv_path = \"chat_logs.csv\"   # 🔁 Update this path\n", "chat_logs = pd.read_csv(csv_path)\n", "chat_logs"]}, {"cell_type": "code", "execution_count": 18, "id": "f6388d98-d452-4a76-b7d1-45f78df13ada", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 0 new unique chat log(s) inserted successfully!\n"]}], "source": ["from mongoengine import connect, Document, StringField, IntField, DateTimeField, DoesNotExist\n", "from dotenv import load_dotenv\n", "import os\n", "import pandas as pd\n", "from datetime import datetime\n", "\n", "# Load .env environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")   \n", "MONGODB_DATABASE_NAME = os.getenv(\"MONGODB_DATABASE_NAME\")\n", "\n", "# Connect to MongoDB\n", "connect(db=MONGODB_DATABASE_NAME, host=MONGODB_DATABASE_URL)\n", "\n", "# Assume `chat_logs` DataFrame is already defined\n", "# Convert time_stamp to datetime object\n", "chat_logs[\"time_stamp\"] = pd.to_datetime(chat_logs[\"time_stamp\"])\n", "\n", "# Define MongoDB Document\n", "class ChatLog(Document):\n", "    id = IntField(primary_key=True)\n", "    session_id = StringField()\n", "    user_message = StringField()\n", "    bot_message = StringField()\n", "    date = StringField()  # Format: YYYY-MM-DD\n", "    time_stamp = DateTimeField()\n", "\n", "    meta = {'collection': 'chat_logs'}\n", "\n", "# Insert unique records\n", "inserted_count = 0\n", "for idx, row in chat_logs.iterrows():\n", "    try:\n", "        ChatLog.objects.get(id=idx)\n", "    except DoesNotExist:\n", "        log = ChatLog(\n", "            id=idx,\n", "            session_id=row['session_id'],\n", "            user_message=row['user_message'],\n", "            bot_message=row['bot_message'],\n", "            date=row['date'],\n", "            time_stamp=row['time_stamp']\n", "        )\n", "        log.save()\n", "        inserted_count += 1\n", "\n", "print(f\"✅ {inserted_count} new unique chat log(s) inserted successfully!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d25741b2-0e16-4e05-ace9-7697ea3cfd8d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "54e0e53a-a9e4-4018-b521-e0c46fc01d76", "metadata": {}, "source": ["### Get all the schemas from DB :"]}, {"cell_type": "code", "execution_count": 88, "id": "a4d8f51f-53f5-4f6c-a323-b84c626483f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pymongo in /Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/lib/python3.9/site-packages (4.13.2)\n", "Collecting pydantic\n", "  Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)\n", "\u001b[K     |████████████████████████████████| 444 kB 1.3 MB/s eta 0:00:01\n", "\u001b[?25hRequirement already satisfied: dnspython<3.0.0,>=1.16.0 in /Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/lib/python3.9/site-packages (from pymongo) (2.7.0)\n", "Collecting annotated-types>=0.6.0\n", "  Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)\n", "Collecting pydantic-core==2.33.2\n", "  Downloading pydantic_core-2.33.2-cp39-cp39-macosx_11_0_arm64.whl (1.9 MB)\n", "\u001b[K     |████████████████████████████████| 1.9 MB 2.5 MB/s eta 0:00:01\n", "\u001b[?25hCollecting typing-inspection>=0.4.0\n", "  Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)\n", "Requirement already satisfied: typing-extensions>=4.12.2 in /Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/lib/python3.9/site-packages (from pydantic) (4.14.1)\n", "Installing collected packages: typing-inspection, pydantic-core, annotated-types, pydantic\n", "Successfully installed annotated-types-0.7.0 pydantic-2.11.7 pydantic-core-2.33.2 typing-inspection-0.4.1\n", "\u001b[33mWARNING: You are using pip version 21.2.4; however, version 25.1.1 is available.\n", "You should consider upgrading via the '/Users/<USER>/Projects/Portfolios/Data-Insertion-Module/venv/bin/python3 -m pip install --upgrade pip' command.\u001b[0m\n"]}], "source": ["!pip install pymongo pydantic"]}, {"cell_type": "code", "execution_count": 19, "id": "b19620c4-fc61-4b63-a454-02853c02c689", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "# ✅ Pydantic model for collection: `experiences`\n", "class Experiences(BaseModel):\n", "    company: Optional[str] = Field(None)\n", "    position: Optional[str] = Field(None)\n", "    description: Optional[str] = Field(None)\n", "    location: Optional[str] = Field(None)\n", "    achievements: Optional[str] = Field(None)\n", "    company_url: Optional[str] = Field(None)\n", "    start_date: Optional[str] = Field(None)\n", "    end_date: Optional[str] = Field(None)\n", "    technologies: Optional[str] = Field(None)\n", "    logo: Optional[str] = Field(None)\n", "----------------------------------------------------------------------------------------------------\n", "\n", "# ✅ Pydantic model for collection: `skills`\n", "class Skills(BaseModel):\n", "    name: Optional[str] = Field(None)\n", "    level: Optional[str] = Field(None)\n", "    category: Optional[str] = Field(None)\n", "----------------------------------------------------------------------------------------------------\n", "\n", "# ✅ Pydantic model for collection: `chat_logs`\n", "class Chat_logs(BaseModel):\n", "    session_id: Optional[str] = Field(None)\n", "    user_message: Optional[str] = Field(None)\n", "    bot_message: Optional[str] = Field(None)\n", "    date: Optional[str] = Field(None)\n", "    time_stamp: Optional[str] = Field(None)\n", "----------------------------------------------------------------------------------------------------\n", "\n", "# ✅ Pydantic model for collection: `projects`\n", "class Projects(BaseModel):\n", "    title: Optional[str] = Field(None)\n", "    description: Optional[str] = Field(None)\n", "    category: Optional[str] = Field(None)\n", "    date: Optional[str] = Field(None)\n", "    image_url: Optional[str] = Field(None)\n", "    technologies: Optional[str] = Field(None)\n", "    github_url: Optional[str] = Field(None)\n", "    live_url: Optional[str] = Field(None)\n", "----------------------------------------------------------------------------------------------------\n", "\n", "# ✅ Pydantic model for collection: `email_submissions`\n", "class Email_submissions(BaseModel):\n", "    name: Optional[str] = Field(None)\n", "    email: Optional[str] = Field(None)\n", "    subject: Optional[str] = Field(None)\n", "    message: Optional[str] = Field(None)\n", "    date: Optional[str] = Field(None)\n", "    time_stamp: Optional[str] = Field(None)\n", "----------------------------------------------------------------------------------------------------\n", "\n", "# ✅ Pydantic model for collection: `education`\n", "class Education(BaseModel):\n", "    degree: Optional[str] = Field(None)\n", "    field: Optional[str] = Field(None)\n", "    institution: Optional[str] = Field(None)\n", "    description: Optional[str] = Field(None)\n", "    location: Optional[str] = Field(None)\n", "    start_date: Optional[int] = Field(None)\n", "    end_date: Optional[int] = Field(None)\n", "    achievements: Optional[str] = Field(None)\n", "    institution_url: Optional[str] = Field(None)\n", "    logo: Optional[str] = Field(None)\n", "----------------------------------------------------------------------------------------------------\n", "\n", "# ✅ Pydantic model for collection: `certifications`\n", "class Certifications(BaseModel):\n", "    title: Optional[str] = Field(None)\n", "    issuer: Optional[str] = Field(None)\n", "    date: Optional[str] = Field(None)\n", "    verification_url: Optional[str] = Field(None)\n", "    icon: Optional[str] = Field(None)\n", "----------------------------------------------------------------------------------------------------\n", "\n", "# ✅ Pydantic model for collection: `publications`\n", "class Publications(BaseModel):\n", "    title: Optional[str] = Field(None)\n", "    authors: Optional[str] = Field(None)\n", "    abstract: Optional[str] = Field(None)\n", "    issue: Optional[str] = Field(None)\n", "    category: Optional[str] = Field(None)\n", "    pages: Optional[str] = Field(None)\n", "    year: Optional[int] = Field(None)\n", "    keywords: Optional[str] = Field(None)\n", "    published: Optional[bool] = Field(None)\n", "    external_link: Optional[str] = Field(None)\n", "    pdf_link: Optional[str] = Field(None)\n", "    journal: Optional[str] = Field(None)\n", "    volume: Optional[str] = Field(None)\n", "    doi: Optional[str] = Field(None)\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["from pymongo import MongoClient\n", "from pydantic import BaseModel, Field\n", "from typing import Optional\n", "import os\n", "from dotenv import load_dotenv\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "MONGODB_DATABASE_URL = os.getenv(\"MONGODB_DATABASE_URL\")\n", "MONGODB_DATABASE_NAME = \"website-data\"\n", "\n", "# Connect to MongoDB\n", "client = MongoClient(MONGODB_DATABASE_URL)\n", "db = client[MONGODB_DATABASE_NAME]\n", "\n", "# Helper to infer Python types from values\n", "def infer_type(value):\n", "    if isinstance(value, bool):\n", "        return \"bool\"\n", "    elif isinstance(value, int):\n", "        return \"int\"\n", "    elif isinstance(value, float):\n", "        return \"float\"\n", "    elif isinstance(value, list):\n", "        return \"list\"\n", "    elif isinstance(value, dict):\n", "        return \"dict\"\n", "    else:\n", "        return \"str\"\n", "\n", "# Loop through all collections\n", "for collection_name in db.list_collection_names():\n", "    collection = db[collection_name]\n", "    sample_doc = collection.find_one()\n", "\n", "    if not sample_doc:\n", "        print(f\"# ❌ Skipping empty collection: {collection_name}\")\n", "        continue\n", "\n", "    print(f\"\\n# ✅ Pydantic model for collection: `{collection_name}`\")\n", "\n", "    print(f\"class {collection_name.capitalize()}(BaseModel):\")\n", "    for key, value in sample_doc.items():\n", "        if key == \"_id\":\n", "            continue  # skip MongoDB internal ID\n", "        py_type = infer_type(value)\n", "        # Use Optional because not all documents may have this field\n", "        print(f\"    {key}: Optional[{py_type}] = Field(None)\")\n", "    print(\"-\"*100)\n"]}, {"cell_type": "code", "execution_count": null, "id": "e8efd83c-d3fa-45e0-b15c-5310bc482307", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}